<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class () extends Migration {
    public function up(): void
    {
        // Add suitable_for column to properties table
        if (Schema::hasTable('re_properties') && !Schema::hasColumn('re_properties', 'suitable_for')) {
            Schema::table('re_properties', function (Blueprint $table): void {
                $table->json('suitable_for')->nullable()->after('required_documents');
            });
        }

        // Migrate existing data from pivot table to new column
        if (Schema::hasTable('re_property_suitable') && Schema::hasTable('re_suitable')) {
            $this->migrateExistingData();
        }
    }

    public function down(): void
    {
        // Remove suitable_for column
        if (Schema::hasColumn('re_properties', 'suitable_for')) {
            Schema::table('re_properties', function (Blueprint $table): void {
                $table->dropColumn('suitable_for');
            });
        }
    }

    private function migrateExistingData(): void
    {
        // Get all properties with their suitable relationships
        $properties = DB::table('re_properties')
            ->select('id')
            ->get();

        foreach ($properties as $property) {
            // Get suitable items for this property
            $suitableItems = DB::table('re_property_suitable')
                ->join('re_suitable', 're_property_suitable.suitable_id', '=', 're_suitable.id')
                ->where('re_property_suitable.property_id', $property->id)
                ->pluck('re_suitable.name')
                ->toArray();

            // Map existing names to enum values
            $suitableForValues = [];
            foreach ($suitableItems as $name) {
                $enumValue = $this->mapNameToEnumValue($name);
                if ($enumValue) {
                    $suitableForValues[] = $enumValue;
                }
            }

            // Update property with new suitable_for values
            if (!empty($suitableForValues)) {
                DB::table('re_properties')
                    ->where('id', $property->id)
                    ->update(['suitable_for' => json_encode(array_unique($suitableForValues))]);
            }
        }
    }

    private function mapNameToEnumValue(string $name): ?string
    {
        $name = strtolower(trim($name));
        
        // Map common variations to enum values
        $mappings = [
            'students' => 'students',
            'student' => 'students',
            'professionals' => 'professionals',
            'professional' => 'professionals',
            'families' => 'families',
            'family' => 'families',
            'for families' => 'families',
            'for family' => 'families',
        ];

        return $mappings[$name] ?? null;
    }
};
