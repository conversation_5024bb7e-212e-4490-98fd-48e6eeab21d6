<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class () extends Migration {
    public function up(): void
    {
        // Drop the pivot tables first (foreign key constraints)
        Schema::dropIfExists('re_property_suitable');
        Schema::dropIfExists('re_project_suitable');
        
        // Drop the main suitable table
        Schema::dropIfExists('re_suitable');
    }

    public function down(): void
    {
        // Recreate the tables if needed for rollback
        if (! Schema::hasTable('re_suitable')) {
            Schema::create('re_suitable', function (Blueprint $table): void {
                $table->id();
                $table->string('name', 120);
                $table->string('icon', 60)->nullable();
                $table->string('status', 60)->default('published');
            });
        }

        if (! Schema::hasTable('re_property_suitable')) {
            Schema::create('re_property_suitable', function (Blueprint $table): void {
                $table->foreignId('property_id');
                $table->foreignId('suitable_id');
                $table->primary(['property_id', 'suitable_id'], 'property_suitable_primary');
            });
        }

        if (! Schema::hasTable('re_project_suitable')) {
            Schema::create('re_project_suitable', function (Blueprint $table): void {
                $table->foreignId('project_id');
                $table->foreignId('suitable_id');
                $table->primary(['project_id', 'suitable_id'], 'project_suitable_primary');
            });
        }
    }
};
