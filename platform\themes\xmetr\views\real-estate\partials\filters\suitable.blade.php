@php
    use Xmetr\RealEstate\Enums\SuitableForEnum;

    $suitable = collect(SuitableForEnum::labels())->map(function ($label, $value) {
        return (object) [
            'id' => $value,
            'name' => $label,
        ];
    });

    $asGrid ??= true;
@endphp

<div class="group-checkbox">
    <div class="text-1">{{ __('Suitable For:') }}</div>
    <div @class(['group-amenities', 'mt-8' => $asGrid])>
        @if($asGrid)
            <div class="row row-cols-2 row-cols-sm-3 row-cols-lg-4 row-cols-xl-6 box-amenities g-3">
        @endif
            @foreach($suitable as $suitableItem)
                @if($asGrid)
                <div class="col">
                @endif
                    <fieldset class="amenities-item">
                        <input type="checkbox" name="suitable[]" class="tf-checkbox style-1" id="suitable-{{ $suitableItem->id }}" value="{{ $suitableItem->id }}" @checked(in_array($suitableItem->id, request()->query('suitable', []))) />
                        <label for="suitable-{{ $suitableItem->id }}" class="text-cb-amenities">{{ $suitableItem->name }}</label>
                    </fieldset>
                @if($asGrid)
                </div>
                @endif
            @endforeach
        @if($asGrid)
            </div>
        @endif
    </div>
</div>
