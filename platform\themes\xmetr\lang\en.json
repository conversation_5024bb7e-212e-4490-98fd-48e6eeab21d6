{"-- None --": "-- None --", "-- Select --": "-- Select --", "1 Bathroom": "1 Bathroom", "1 Bedroom": "1 Bedroom", "1 Block": "1 Block", "1 Floor": "1 Floor", "1 Property": "1 Property", "1 Review": "1 Review", "1 Views": "1 Views", "1 bathroom": "1 bathroom", "1 bedroom": "1 bedroom", "1 credit": "1 credit", "1 post": "1 post", "bedroom": "bedroom", "bathroom": "bathroom", "block": "block", "floor": "floor", "property": "property", "404 - Page Not found": "404 - Page Not found", "404 Page Not Found": "404 Page Not Found", "Found": "Found", "5+ blocks": "5+ blocks", "5+ floors": "5+ floors", "5+ rooms": "5+ rooms", "500 Internal Server Error": "500 Internal Server Error", "503 Service Unavailable": "503 Service Unavailable", ":avg out of 5": ":avg out of 5", ":count Properties": ":count Properties", ":count Review(s)": ":count Review(s)", ":count bathrooms": ":count bathrooms", ":count bedrooms": ":count bedrooms", ":count credits": ":count credits", ":count properties": ":count properties", ":count property": ":count property", ":name doesn't support :currency. List of currencies supported by :name: :currencies.": ":name doesn't support :currency. List of currencies supported by :name: :currencies.", ":name feed": ":name feed", ":name font family": ":name font family", ":name font size": ":name font size", ":number Bathrooms": ":number Bathrooms", ":number Bedrooms": ":number Bedrooms", ":number Blocks": ":number Blocks", ":number Column": ":number Column", ":number Columns": ":number Columns", ":number Floors": ":number Floors", ":number Reviews": ":number Reviews", ":number Views": ":number Views", ":number posts": ":number posts", ":number+ Bathrooms": ":number+ Bathrooms", ":number+ Bedrooms": ":number+ Bedrooms", ":number+ Blocks": ":number+ Blocks", ":number+ Floors": ":number+ Floors", ":price / per post": ":price / per post", ":price Total :percentage_sale": ":price Total :percentage_sale", "A content tab to display information with a title.": "A content tab to display information with a title.", "A new version (:version / released on :date) is available to update!": "A new version (:version / released on :date) is available to update!", "A-Z": "A-Z", "API Key": "API Key", "About": "About", "About Agent": "About Agent", "About Us": "About Us", "Accept and install": "Accept and install", "Action": "Action", "Add": "Add", "Add listing": "Add listing", "Edit listing": "Edit listing", "Edit profile": "Edit profile", "Featured Amenity": "Featured Amenity", "This icon will be used in the frontend for featured amenities.": "This icon will be used in the frontend for featured amenities.", "Add Google Maps iframe": "Add Google Maps iframe", "Add YouTube video": "Add YouTube video", "Add a custom menu to your widget area.": "Add a custom menu to your widget area.", "Add credit to account": "Add credit to account", "Add custom HTML content": "Add custom HTML content", "Add new": "Add new", "Add widgets here to appear in the sidebar of your blog pages.": "Add widgets here to appear in the sidebar of your blog pages.", "Added \":name\" to wishlist successfully!": "Added \":name\" to wishlist successfully!", "Added :credits credit(s) by admin \":user\"": "Added :credits credit(s) by admin \":user\"", "Address": "Address", "Ads": "Ads", "Advanced": "Advanced", "After registration at :name, you will have API key": "After registration at :name, you will have API key", "After registration at :name, you will have Client ID, Client Secret": "After registration at :name, you will have Client ID, Client Secret", "After registration at :name, you will have Public & Secret keys": "After registration at :name, you will have Public & Secret keys", "After registration at :name, you will have Store ID and Store Password (API/Secret key)": "After registration at :name, you will have Store ID and Store Password (API/Secret key)", "Agent": "Agent", "Owner": "Owner", "Agents": "Agents", "User Speak": "User Speak", "Rent Apartment": "Rent Apartment", "All": "All", "All Pages": "All Pages", "All flats": "All flats", "All squares": "All squares", "Filters": "Filters", "Already have an account?": "Already have an account?", "Have an account?": "Have an account?", "Amenities": "Amenities", "Amenities and features": "Amenities and features", "Amenities:": "Amenities:", "An error occurred while trying to login": "An error occurred while trying to login", "Animation text": "Animation text", "Applied coupon \":code\" successfully!": "Applied coupon \":code\" successfully!", "Apply Now": "Apply Now", "Are you sure you want to renew this property": "Are you sure you want to renew this property", "Are you sure you want to renew this property, it will takes 1 credit from your credits": "Are you sure you want to renew this property, it will takes 1 credit from your credits", "Are you sure you want to turn off the debug mode? This action cannot be undone.": "Are you sure you want to turn off the debug mode? This action cannot be undone.", "Are you sure?": "Are you sure?", "Audio File": "Audio File", "Author": "Author", "Autoplay speed (if autoplay enabled)": "Autoplay speed (if autoplay enabled)", "Back To Home": "Back To Home", "Back to login page": "Back to login page", "Background Image": "Background Image", "Background color": "Background color", "Background image": "Background image", "Banner": "Banner", "Bathrooms": "Bathrooms", "Bathrooms:": "Bathrooms:", "Bedrooms": "Bedrooms", "Bedrooms:": "Bedrooms:", "Bill payment user account information, ex: account.name, account.email, ...": "Bill payment user account information, ex: account.name, account.email, ...", "Blocks": "Blocks", "Blocks:": "Blocks:", "Blog Categories": "Blog Categories", "Blog List (after)": "Blog List (after)", "Blog List (before)": "Blog List (before)", "Blog Posts": "Blog Posts", "Blog Search": "Blog Search", "Blog Sidebar": "Blog Sidebar", "Blog Sidebar (after)": "Blog Sidebar (after)", "Blog Sidebar (before)": "Blog Sidebar (before)", "Body": "Body", "Bottom Footer Sidebar": "Bottom Footer Sidebar", "Bottom Post Detail Sidebar": "Bottom Post Detail Sidebar", "Bottom footer section for legal notices and credits.": "Bottom footer section for legal notices and credits.", "Breadcrumb": "Breadcrumb", "Breadcrumb background color": "Breadcrumb background color", "Breadcrumb background image": "Breadcrumb background image", "Breadcrumb text color": "Breadcrumb text color", "Business hours": "Business hours", "Button URL": "Button URL", "Button label": "Button label", "Buy credits": "Buy credits", "Call To Action": "Call To Action", "Can't send message on this time, please try again later!": "Can't send message on this time, please try again later!", "Cancel": "Cancel", "Cannot find this account!": "Cannot find this account!", "Cannot login, no email provided!": "Cannot login, no email provided!", "Captcha": "<PERSON><PERSON>", "Captcha Verification Failed!": "Captcha Verification Failed!", "Caption": "Caption", "Careers": "Careers", "Categories": "Categories", "Category": "Category", "Center content": "Center content", "Change copyright": "Change copyright", "Check have enabled the invoice stamp": "Check have enabled the invoice stamp", "Check site is using custom font for invoice or not": "Check site is using custom font for invoice or not", "Checklist": "Checklist", "Checkout": "Checkout", "Checkout error!": "Checkout error!", "Checkout successfully!": "Checkout successfully!", "Choices": "Choices", "Choose The Package": "<PERSON>ose The Package", "Choose agents": "Choose agents", "Choose categories": "Choose categories", "Choose date format for your front theme.": "Choose date format for your front theme.", "Cities": "Cities", "City": "City", "City, State": "City, State", "Clear": "Clear", "Close": "Close", "Color": "Color", "Coming Soon": "Coming Soon", "Company Agent at :company": "Company Agent at :company", "Contact": "Contact", "Contact Agency": "Contact Agency", "Contact agency": "Contact agency", "Content": "Content", "Content Quote": "Content Quote", "Content Tab": "Content Tab", "Content image": "Content image", "Continuously": "Continuously", "Copy link": "Copy link", "Copyright": "Copyright", "Copyright on footer of site. Using %Y to display current year.": "Copyright on footer of site. Using %Y to display current year.", "Copyright text at the bottom footer.": "Copyright text at the bottom footer.", "Could not download updated file. Please check your license or your internet network.": "Could not download updated file. Please check your license or your internet network.", "Could not update files & database.": "Could not update files & database.", "Countdown time": "Countdown time", "Counters": "Counters", "Coupon code: :code": "Coupon code: :code", "Create engaging call-to-action sections with customizable headings, buttons, and images.": "Create engaging call-to-action sections with customizable headings, buttons, and images.", "Credits": "Credits", "Credits: :count": "Credits: :count", "Currencies": "Currencies", "Custom HTML": "Custom HTML", "Custom Menu": "Custom Menu", "Customer can buy product and pay directly using Visa, Credit card via :name": "Customer can buy product and pay directly using Visa, Credit card via :name", "Date format": "Date format", "Days": "Days", "Default": "<PERSON><PERSON><PERSON>", "Default search type": "Default search type", "Delete ads.txt file": "Delete ads.txt file", "Delete property successfully!": "Delete property successfully!", "Description": "Description", "Destination type": "Destination type", "Detail Page (after)": "Detail <PERSON> (after)", "Detail Page (before)": "Detail <PERSON> (before)", "Detail Page Sidebar (after)": "Detail Page <PERSON>bar (after)", "Detail Page Sidebar (before)": "Detail Page Sidebar (before)", "Dismiss": "<PERSON><PERSON><PERSON>", "Display Newsletter form on sidebar": "Display Newsletter form on sidebar", "Display blog posts": "Display blog posts", "Display on pages": "Display on pages", "Display posts count?": "Display posts count?", "Display recent blog posts": "Display recent blog posts", "Display type": "Display type", "Displays a list of posts related to the current content.": "Displays a list of posts related to the current content.", "Displays a set of services in a tabbed format. Each tab represents a service and includes fields for title, description, icon, ...": "Displays a set of services in a tabbed format. Each tab represents a service and includes fields for title, description, icon, ...", "Distance key between facilities": "Distance key between facilities", "Do you want to delete this image?": "Do you want to delete this image?", "Don't have an account?": "Don't have an account?", "Don't show this popup again": "Don't show this popup again", "Dynamic carousel for featured content with customizable links.": "Dynamic carousel for featured content with customizable links.", "Edit this agent": "Edit this agent", "Edit this project": "Edit this project", "Edit this property": "Edit this property", "Edit this shortcode": "Edit this shortcode", "Edit this widget": "Edit this widget", "Edit": "Edit", "Recover": "Recover", "Rented": "Rented", "Active": "Active", "Email": "Email", "Email Address": "Email Address", "Email address": "Email address", "Enable Facebook chat?": "Enable Facebook chat?", "Enable Facebook comment in post detail page?": "Enable Facebook comment in post detail page?", "Enable Facebook comment in project detail page?": "Enable Facebook comment in project detail page?", "Enable Facebook comment in property detail page?": "Enable Facebook comment in property detail page?", "Enable Newsletter Popup": "Enable Newsletter Popup", "Enable Preloader?": "Enable Preloader?", "Enable back to top button": "Enable back to top button", "Enable dark mode": "Enable dark mode", "Enable lazy loading": "Enable lazy loading", "Enable light mode": "Enable light mode", "Enable search box": "Enable search box", "Enable search projects on search box": "Enable search projects on search box", "Enable sticky header": "Enable sticky header", "Enter API key into the box in right hand": "Enter API key into the box in right hand", "Enter Client ID, Secret into the box in right hand": "Enter Client ID, Secret into the box in right hand", "Enter Public, Secret into the box in right hand": "Enter Public, Secret into the box in right hand", "Enter Store ID and Store Password (API/Secret key) into the box in right hand": "Enter Store ID and Store Password (API/Secret key) into the box in right hand", "Enter Your Email": "Enter Your Email", "Enter checklist here, saperated by commas (,)": "Enter checklist here, saperated by commas (,)", "Enter keyword...": "Enter keyword...", "Enter your message": "Enter your message", "Enter your message...": "Enter your message...", "Error": "Error", "Error when processing payment via :paymentType!": "Error when processing payment via :paymentType!", "Ex: 60-Day Job Postings": "Ex: 60-Day Job Postings", "Expand the content of the first FAQ": "Expand the content of the first FAQ", "Explore Now": "Explore Now", "Explore nearby amenities to precisely locate your property and identify surrounding conveniences, providing a comprehensive overview of the living environment and the property's convenience.": "Explore nearby amenities to precisely locate your property and identify surrounding conveniences, providing a comprehensive overview of the living environment and the property's convenience.", "FAQ categories": "FAQ categories", "FAQs": "FAQs", "Facebook": "Facebook", "Facebook Admin ID": "Facebook Admin ID", "Facebook Admins": "Facebook Admins", "Facebook App ID": "Facebook App ID", "Facebook Integration": "Facebook Integration", "Facebook admins to manage comments :link": "Facebook admins to manage comments :link", "Facebook page ID": "Facebook page ID", "Featured": "Featured", "Featured projects": "Featured projects", "Featured properties": "Featured properties", "Features": "Features", "Filter": "Filter", "Filter box on the left": "Filter box on the left", "Find Projects": "Find Projects", "Find Properties": "Find Properties", "First name": "First name", "Fix it for me": "Fix it for me", "Flat Range": "Flat Range", "Flat from": "Flat from", "Flat to": "Flat to", "Flats:": "Flats:", "Floor Plans": "Floor Plans", "Floor plans": "Floor plans", "Floors": "Floors", "Floors:": "Floors:", "Follow Us": "Follow Us", "Follow Us:": "Follow Us:", "Footer (after)": "Footer (after)", "Footer (before)": "Footer (before)", "Footer background color": "Footer background color", "Footer background image": "Footer background image", "For Rent": "For Rent", "For Sale": "For Sale", "For devices with width from 768px to 1200px, if empty, will use the image from the desktop.": "For devices with width from 768px to 1200px, if empty, will use the image from the desktop.", "For devices with width less than 768px, if empty, will use the image from the tablet.": "For devices with width less than 768px, if empty, will use the image from the tablet.", "Forgot Password": "Forgot Password", "Forgot password?": "Forgot password?", "Free": "Free", "Free :number post(s)": "Free :number post(s)", "From": "From", "Full Width": "Full Width", "Functions": "Functions", "Gallery": "Gallery", "Go to :link to change the copyright text.": "Go to :link to change the copyright text.", "Go to homepage": "Go to homepage", "Google Maps": "Google Maps", "Grid": "Grid", "Group by category": "Group by category", "HTML code": "HTML code", "Header (after)": "Header (after)", "Header (before)": "Header (before)", "Heading": "Heading", "Heading 1": "Heading 1", "Heading 2": "Heading 2", "Heading 3": "Heading 3", "Heading 4": "Heading 4", "Heading 5": "Heading 5", "Heading 6": "Heading 6", "Height": "Height", "Hello": "Hello", "Hero Banner": "Hero Banner", "Hide Advanced": "Hide Advanced", "Home": "Home", "Homepage": "Homepage", "Hotline": "Hotline", "Hours": "Hours", "Hover color": "Hover color", "I agree to the :link": "I agree to the :link", "I agree to the Terms and Privacy Policy": "I agree to the Terms and Privacy Policy", "Icon": "Icon", "Icon Image (It will override icon above if set)": "Icon Image (It will override icon above if set)", "Icon image": "Icon image", "Icon image (It will override icon above if set)": "Icon image (It will override icon above if set)", "If a Video URL is provided, a play icon will appear on the image, allowing users to click and play the video.": "If a Video URL is provided, a play icon will appear on the image, allowing users to click and play the video.", "If icon image is set, it will be used instead of the icon above.": "If icon image is set, it will be used instead of the icon above.", "If you are the administrator and you can't access your site after enabling maintenance mode, just need to delete file <strong>storage/framework/down</strong> to turn-off maintenance mode.": "If you are the administrator and you can't access your site after enabling maintenance mode, just need to delete file <strong>storage/framework/down</strong> to turn-off maintenance mode.", "If you need help, contact us at :mail.": "If you need help, contact us at :mail.", "If you select an image, the background color will be ignored.": "If you select an image, the background color will be ignored.", "If you use the YouTube video link above, the thumbnail will be automatically obtained.": "If you use the YouTube video link above, the thumbnail will be automatically obtained.", "Image": "Image", "Image Slider": "Image Slider", "Information box title": "Information box title", "Inner Footer Sidebar": "Inner Footer Sidebar", "Inner footer section for site info, menus, and newsletter.": "Inner footer section for site info, menus, and newsletter.", "Install plugin from Marketplace": "Install plugin from Marketplace", "Internal Server Error": "Internal Server Error", "Invalid Data!": "Invalid Data!", "Invalid Transaction!": "Invalid Transaction!", "Invalid step.": "Invalid step.", "InvalidStateException occurred while trying to login": "InvalidStateException occurred while trying to login", "Investor": "Investor", "Investor:": "Investor:", "Invoice detail :code": "Invoice detail :code", "Invoice information from database, ex: invoice.code, invoice.amount, ...": "Invoice information from database, ex: invoice.code, invoice.amount, ...", "Invoices": "Invoices", "Is autoplay?": "Is autoplay?", "It looks as through there are no activities here.": "It looks as through there are no activities here.", "It will replace Icon Font if it is present.": "It will replace Icon Font if it is present.", "Items per row": "Items per row", "Job summary": "Job summary", "Jobs": "Jobs", "Joined": "Joined", "Joined on :date": "Joined on :date", "Key": "Key", "Keyword": "Keyword", "Label": "Label", "Last Updated": "Last Updated", "Last name": "Last name", "Latest": "Latest", "Latest Properties": "Latest Properties", "Latest posts from :site_title": "Latest posts from :site_title", "Latitude longitude center on properties page": "Latitude longitude center on properties page", "Lazy load images": "Lazy load images", "Lazy load placeholder image": "Lazy load placeholder image", "Learn More": "Learn More", "Learn more": "Learn more", "Learn more about Twig template: :url": "Learn more about Twig template: :url", "Leave categories empty if you want to show posts from all categories.": "Leave categories empty if you want to show posts from all categories.", "License": "License", "License Activation": "License Activation", "Limit": "Limit", "LinkedIn": "LinkedIn", "List": "List", "List with map on the right": "List with map on the right", "List with map on top": "List with map on top", "Listing Page (after)": "Listing Page (after)", "Listing Page (before)": "Listing Page (before)", "listing": "listing", "Location": "Location", "Similar listings": "Similar listings", ":count photo": ":count photo", "Location type": "Location type", "Country": "Country", "Choose Country": "Choose Country", "Choose City": "Choose City", "Price $/month": "Price $/month", "Login": "<PERSON><PERSON>", "Login to your account": "Login to your account", "Login with social networks": "Login with social networks", "Logo height (px)": "Logo height (px)", "Logo light": "Logo light", "Looks like there are no reviews!": "Looks like there are no reviews!", "Loop?": "Loop?", "Lost your password? Please enter your username or email address. You will receive a link to create a new password via email.": "Lost your password? Please enter your username or email address. You will receive a link to create a new password via email.", "Main header background color": "Main header background color", "Main header border color": "Main header border color", "Main header text color": "Main header text color", "Manage Invoices": "Manage Invoices", "Manage Social Links in Appearance → Theme Options → Social links.": "Manage Social Links in Appearance → Theme Options → Social links.", "Manage the social links in Theme Options -> Social Links": "Manage the social links in Theme Options -> Social Links", "Manual Transaction": "Manual Transaction", "Map": "Map", "Math Captcha": "<PERSON>", "Math Captcha Verification Failed!": "Math Captcha Verification Failed!", "Media - Audio": "Media - Audio", "Media - Video": "Media - Video", "Media URL": "Media URL", "Menu": "<PERSON><PERSON>", "Message": "Message", "Minutes": "Minutes", "Mobile Image": "Mobile Image", "More Job Openings": "More Job Openings", "More properties by this agent": "More properties by this agent", "My Profile": "My Profile", "Profile": "Profile", "My Wishlist": "My Wishlist", "My favorites": "My favorites", "Commission": "Commission", "Deposit": "<PERSON><PERSON><PERSON><PERSON>", "My listings": "My listings", "listings": "listings", "Search by parameters": "Search by parameters", "Object type": "Object type", "Clear All": "Clear All", "Log out": "Log out", "Log in": "Log in", "Sign in": "Sign in", "Sign up": "Sign up", "Name": "Name", "Name (A-Z)": "Name (A-Z)", "Name (Z-A)": "Name (Z-A)", "Nearby": "Nearby", "Newest": "Newest", "Newsletter Popup": "Newsletter Popup", "Newsletter form": "Newsletter form", "Next": "Next", "No": "No", "No Layout": "No Layout", "No cities found": "No cities found", "No payment charge. Please try again!": "No payment charge. Please try again!", "No project found": "No project found", "No projects found.": "No projects found.", "No properties found.": "No properties found.", "No results found": "No results found", "No suggestion found": "No suggestion found", "No transactions!": "No transactions!", "Number": "Number", "Number of bathrooms": "Number of bathrooms", "Number of bedrooms": "Number of bedrooms", "Number of blocks": "Number of blocks", "Number of columns": "Number of columns", "Number of credits": "Number of credits", "Number of flats": "Number of flats", "Number of floors": "Number of floors", "Number of items to display": "Number of items to display", "Number of projects per page": "Number of projects per page", "Number of properties per page": "Number of properties per page", "Number of related projects": "Number of related projects", "Number of related properties": "Number of related properties", "Number tags to display": "Number tags to display", "OK": "OK", "Oldest": "Oldest", "Only show featured properties": "Only show featured properties", "Oops… You just found an error page!": "Oops… You just found an error page!", "Open URL in a new tab": "Open URL in a new tab", "Open user menu": "Open user menu", "Overview": "Overview", "PHP version :version required": "PHP version :version required", "Package information": "Package information", "Packages": "Packages", "Page could not be found": "Page could not be found", "Password": "Password", "Password confirmation": "Password confirmation", "Payment Type": "Payment Type", "Payment description": "Payment description", "Payment failed!": "Payment failed!", "Payment method": "Payment method", "Payment status": "Payment status", "Payment with :paymentType": "Payment with :paymentType", "Phone": "Phone", "Phone (optional)": "Phone (optional)", "Phone number": "Phone number", "Pinterest": "Pinterest", "Place widgets here to display additional content below individual blog posts.": "Place widgets here to display additional content below individual blog posts.", "Please log in to write review!": "Please log in to write review!", "Please solve the following math function: :label = ?": "Please solve the following math function: :label = ?", "Please switch currency to any supported currency": "Please switch currency to any supported currency", "Please try again in a few minutes, or alternatively return to the homepage by <a href=\":link\">clicking here</a>.": "Please try again in a few minutes, or alternatively return to the homepage by <a href=\":link\">clicking here</a>.", "Popular": "Popular", "Popular tags": "Popular tags", "Popup Delay (seconds)": "<PERSON><PERSON> (seconds)", "Popup Description": "Popup Description", "Popup Image": "Popup Image", "Popup Subtitle": "Popup Subtitle", "Popup Title": "Popup Title", "Post Detail (after)": "Post Detail (after)", "Post Detail (before)": "Post Detail (before)", "Post type": "Post type", "Type": "Type", "Built": "Built", "Area": "Area", "Area м²": "Area м²", "м²": "м²", "Posted at": "Posted at", "Posts": "Posts", "Preloader Version": "Preloader Version", "Previous": "Previous", "Price": "Price", "Price (high to low)": "Price (high to low)", "Price (low to high)": "Price (low to high)", "Price Range": "Price Range", "Price from": "Price from", "Price to": "Price to", "Price:": "Price:", "Pricing Plan": "Pricing Plan", "Primary": "Primary", "Primary color": "Primary color", "Private Notes": "Private Notes", "Processing. Please wait...": "Processing. Please wait...", "Products": "Products", "Project": "Project", "Project ID": "Project ID", "Project ID:": "Project ID:", "Project listing page layout": "Project listing page layout", "Project video": "Project video", "Project's Information": "Project's Information", "Project's information": "Project's information", "Projects": "Projects", "Projects List": "Projects List", "Projects List page": "Projects List page", "Projects in :city": "Projects in :city", "Projects in :state": "Projects in :state", "Properties": "Properties", "Properties For Rent": "Properties For Rent", "Properties For Sale": "Properties For Sale", "Properties List": "Properties List", "Properties List page": "Properties List page", "Properties by this agent": "Properties by this agent", "Properties in :city": "Properties in :city", "Properties in :name": "Properties in :name", "Properties in :state": "Properties in :state", "Properties in project \":name\"": "Properties in project \":name\"", "Property": "Property", "Property Categories": "Property Categories", "Property ID": "Property ID", "Property ID:": "Property ID:", "Property detail page layout": "Property detail page layout", "Property for rent": "Property for rent", "Property for sale": "Property for sale", "Property listing page layout": "Property listing page layout", "Property video": "Property video", "Public Key": "Public Key", "Quantity": "Quantity", "Read More": "Read More", "Read more": "Read more", "Real Estate": "Real Estate", "Recent": "Recent", "Recent Posts": "Recent Posts", "Register": "Register", "Register an account": "Register an account", "Register an account on :name": "Register an account on :name", "Register now": "Register now", "Registered successfully!": "Registered successfully!", "Related Careers": "Related Careers", "Related Posts": "Related Posts", "Related properties": "Related properties", "Remember me": "Remember me", "Remove": "Remove", "Remove image": "Remove image", "Removed \":name\" from wishlist successfully!": "Removed \":name\" from wishlist successfully!", "Removed :credits credit(s) by admin \":user\"": "Removed :credits credit(s) by admin \":user\"", "Removed coupon :code successfully!": "Removed coupon :code successfully!", "Renew": "<PERSON>w", "Renew confirmation": "Renew confirmation", "Renew property successfully": "Renew property successfully", "Reset": "Reset", "Reset Password": "Reset Password", "Review": "Review", "Reviews": "Reviews", "Run": "Run", "Salary": "Salary", "Search": "Search", "Search blog posts": "Search blog posts", "Search for Keyword": "Search for Keyword", "Search for Location": "Search for Location", "Search project": "Search project", "Search result for: \":query\"": "Search result for: \":query\"", "Search...": "Search...", "Seconds": "Seconds", "Secret": "Secret", "Secret Key": "Secret Key", "Select a property to display on the banner.": "Select a property to display on the banner.", "Select background image for this section.": "Select background image for this section.", "Select categories": "Select categories", "Select categories to display as tabs.": "Select categories to display as tabs.", "Selecting an option will redirect you to a list of properties or projects page.": "Selecting an option will redirect you to a list of properties or projects page.", "Send": "Send", "Send Message": "Send Message", "Send Password Reset Link": "Send Password Reset Link", "Send Review": "Send Review", "Send message successfully!": "Send message successfully!", "Separated by commas (,)": "Separated by commas (,)", "Services": "Services", "Set the delay time to show the popup after the page is loaded. Set 0 to show the popup immediately.": "Set the delay time to show the popup after the page is loaded. Set 0 to show the popup immediately.", "Set the height of the logo in pixels. The default value is :default.": "Set the height of the logo in pixels. The default value is :default.", "Setup license code": "Setup license code", "Share on :social": "Share on :social", "Share this project": "Share this project", "Share this property": "Share this property", "Share:": "Share:", "Show author name?": "Show author name?", "Show information box": "Show information box", "Show map on the property/project detail page": "Show map on the property/project detail page", "Show social links": "Show social links", "Showing": "Showing", "Site Copyright": "Site Copyright", "Site information": "Site information", "Site logo": "Site logo", "Slider Image :number": "Slider Image :number", "Slug": "Slug", "Social": "Social", "Social Links": "Social Links", "Social Sharing": "Social Sharing", "Social links": "Social links", "Social sharing buttons": "Social sharing buttons", "Something is broken. Please let us know what you were doing when this error occurred. We will fix it as soon as possible. Sorry for any inconvenience caused.": "Something is broken. Please let us know what you were doing when this error occurred. We will fix it as soon as possible. Sorry for any inconvenience caused.", "Something went wrong.": "Something went wrong.", "Sorry, we are doing some maintenance. Please check back soon.": "Sorry, we are doing some maintenance. Please check back soon.", "Sort by": "Sort by", "Square": "Square", "Square Range": "Square Range", "Square from": "Square from", "Square to": "Square to", "Square:": "Square:", "State": "State", "States": "States", "Status": "Status", "Stop on the last slide": "Stop on the last slide", "Store ID": "Store ID", "Store Password (API/Secret key)": "Store Password (API/Secret key)", "Style": "Style", "Style :number": "Style :number", "Styles": "Styles", "Subject": "Subject", "Submit Property": "Submit Property", "Submit review": "Submit review", "Subscribe": "Subscribe", "Subscribe to newsletter successfully!": "Subscribe to newsletter successfully!", "Subtitle": "Subtitle", "Support native audio": "Support native audio", "Support native video, YouTube, Vimeo, TikTok, X (Twitter)": "Support native video, YouTube, Vimeo, TikTok, X (Twitter)", "Tab #:number": "Tab #:number", "Tablet Image": "Tablet Image", "Tabs": "Tabs", "Tag:": "Tag:", "Tags": "Tags", "Take me home": "Take me home", "Telegram": "Telegram", "Temporarily down for maintenance": "Temporarily down for maintenance", "Term and Privacy Policy URL": "Term and Privacy Policy URL", "Terms and Privacy Policy": "Terms and Privacy Policy", "Testimonials": "Testimonials", "Text": "Text", "The .env file is not writable.": "The .env file is not writable.", "The Most Recent Estate": "The Most Recent Estate", "The company address of invoice": "The company address of invoice", "The company email of invoice": "The company email of invoice", "The company name of invoice": "The company name of invoice", "The company phone number of invoice": "The company phone number of invoice", "The debug mode has been disabled successfully.": "The debug mode has been disabled successfully.", "The debug mode is already disabled.": "The debug mode is already disabled.", "The font family of invoice template": "The font family of invoice template", "The font size in pixels (px). Default is :default": "The font size in pixels (px). Default is :default", "The given email address has not been confirmed. <a href=\":resend_link\">Resend confirmation link.</a>": "The given email address has not been confirmed. <a href=\":resend_link\">Resend confirmation link.</a>", "The page you are looking for could not be found.": "The page you are looking for could not be found.", "The selected :attribute is invalid.": "The selected :attribute is invalid.", "The system is up-to-date. There are no new versions to update!": "The system is up-to-date. There are no new versions to update!", "Theme built-in": "Theme built-in", "Theme options": "Theme options", "Theme options for Real Estate": "Theme options for Real Estate", "There is no data to display!": "There is no data to display!", "This coupon is invalid!": "This coupon is invalid!", "This coupon is not used yet!": "This coupon is not used yet!", "This credential is invalid Google Analytics credentials.": "This credential is invalid Google Analytics credentials.", "This feature is temporary disabled in demo mode. Please use another login option. Such as Google.": "This feature is temporary disabled in demo mode. Please use another login option. Such as Google.", "This file is not a valid JSON file.": "This file is not a valid JSON file.", "This image will be used as placeholder for lazy load images.": "This image will be used as placeholder for lazy load images.", "This will replace the icon if it set.": "This will replace the icon if it set.", "Title": "Title", "To": "To", "To show chat box on that website, please go to :link and add :domain to whitelist domains!": "To show chat box on that website, please go to :link and add :domain to whitelist domains!", "Top Footer Sidebar": "Top Footer Sidebar", "Top header background color": "Top header background color", "Top header text color": "Top header text color", "Top section of the footer for logo and social links.": "Top section of the footer for logo and social links.", "Transaction is already successfully completed!": "Transaction is already successfully completed!", "Transaction is successfully completed!": "Transaction is successfully completed!", "Transactions": "Transactions", "URL": "URL", "Unable to set debug mode. No APP_DEBUG variable was found in the .env file.": "Unable to set debug mode. No APP_DEBUG variable was found in the .env file.", "Unknown": "Unknown", "Unsubscribe to newsletter successfully": "Unsubscribe to newsletter successfully", "Update :name": "Update :name", "Upload Service Account JSON File": "Upload Service Account JSON File", "Use Modal for Login/Register": "Use Modal for Login/Register", "Use location in search box as dropdown instead of input auto-complete": "Use location in search box as dropdown instead of input auto-complete", "Use the YouTube video link to be able to watch the video directly on the website.": "Use the YouTube video link to be able to watch the video directly on the website.", "Username": "Username", "Validation Fail!": "Validation Fail!", "Variables": "Variables", "Video": "Video", "Video URL": "Video URL", "Video thumbnail": "Video thumbnail", "View": "View", "View All": "View All", "View All Photos (:count)": "View All Photos (:count)", "View your ads.txt here: :url": "View your ads.txt here: :url", "We have sent you an email to verify your email. Please check and confirm your email address!": "We have sent you an email to verify your email. Please check and confirm your email address!", "We regularly recruit at many positions. See related jobs here": "We regularly recruit at many positions. See related jobs here", "We sent you another confirmation email. You should receive it shortly.": "We sent you another confirmation email. You should receive it shortly.", "WhatsApp": "WhatsApp", "Report": "Report", "What’s nearby?": "What’s nearby?", "When enabled, shortcode content will be loaded sequentially as the page loads, rather than all at once. This can help improve page load times.": "When enabled, shortcode content will be loaded sequentially as the page loads, rather than all at once. This can help improve page load times.", "When the login/register button is clicked, a popup will appear with the login/register form instead of redirecting users to another page.": "When the login/register button is clicked, a popup will appear with the login/register form instead of redirecting users to another page.", "Widget display blog categories": "Widget display blog categories", "Widget display site information": "Widget display site information", "Widget display site logo": "Widget display site logo", "Widget display social links network": "Widget display social links network", "Width": "<PERSON><PERSON><PERSON>", "Wishlist": "Wishlist", "Without map": "Without map", "Write A Review": "Write A Review", "Write a review": "Write a review", "Write comment": "Write comment", "Write your message here": "Write your message here", "X (Twitter)": "X (Twitter)", "Yes": "Yes", "Yes, turn off": "Yes, turn off", "You can change logo in Appearance → Theme Options → Logo.": "You can change logo in Appearance → Theme Options → Logo.", "You can create your app in :link": "You can create your app in :link", "You can get fan page ID using this site :link": "You can get fan page ID using this site :link", "You can use HTML tags. Example: &lt;a href=&quot;tel:0123456789&quot;&gt;0123456789&lt;/a&gt; or &lt;br&gt; for line break": "You can use HTML tags. Example: &lt;a href=&quot;tel:0123456789&quot;&gt;0123456789&lt;/a&gt; or &lt;br&gt; for line break", "You don't have enough credit to renew this property!": "You don't have enough credit to renew this property!", "You have already submitted a review.": "You have already submitted a review.", "You have created a payment #:charge_id via :channel :time : :amount": "You have created a payment #:charge_id via :channel :time : :amount", "You have not added any properties or projects to your wishlist.": "You have not added any properties or projects to your wishlist.", "You have purchased :credits credit(s)": "You have purchased :credits credit(s)", "You need to login to write review.": "You need to login to write review.", "You successfully confirmed your email address.": "You successfully confirmed your email address.", "You will be redirected to :name to complete the payment.": "You will be redirected to :name to complete the payment.", "YouTube": "YouTube", "YouTube URL": "YouTube URL", "YouTube video": "YouTube video", "YouTube, Vimeo, TikTok, ...": "YouTube, <PERSON><PERSON>o, Tik<PERSON>ok, ...", "Your Address": "Your Address", "Your Email": "Your Email", "Your Favorite Projects": "Your Favorite Projects", "Your Favorite Properties": "Your Favorite Properties", "Your Google Adsense ads.txt": "Your Google Adsense ads.txt", "Your Name": "Your Name", "Your Phone": "Your Phone", "Your asset files have been published successfully.": "Your asset files have been published successfully.", "Your email does not exist in the system or you have unsubscribed already!": "Your email does not exist in the system or you have unsubscribed already!", "Your email is in blacklist. Please use another email address.": "Your email is in blacklist. Please use another email address.", "Your file is not found. Please try uploading again.": "Your file is not found. Please try uploading again.", "Your message contains blacklist words: \":words\".": "Your message contains blacklist words: \":words\".", "Your order": "Your order", "Your personal data will be used to support your experience throughout this website, to manage access to your account.": "Your personal data will be used to support your experience throughout this website, to manage access to your account.", "Your review has been submitted!": "Your review has been submitted!", "Your system has been cleaned up successfully.": "Your system has been cleaned up successfully.", "Z-A": "Z-A", "billion": "billion", "blocks": "blocks", "floors": "floors", "free": "free", "from": "from", "ft2": "ft2", "here": "here", "high to low": "high to low", "image": "image", "low to high": "low to high", "million": "million", "m²": "m²", "per post": "per post", "post(s)": "post(s)", "posts": "posts", "room": "room", "rooms": "rooms", "save": "save", "save :percentage %": "save :percentage %", "Standard": "Standard", "to": "to", "total": "total", "via": "via", "views": "views", "yd2": "yd2", "the terms and conditions": "the terms and conditions", "of use of the site": "of use of the site", "By clicking create an account, you accept": "By clicking create an account, you accept", "First time on the site?": "First time on the site?", "Currency & Language": "Currency & Language", "Site Language": "Site Language", "Currency": "<PERSON><PERSON><PERSON><PERSON>", "All Countries": "All Countries", "Explore the World’s Properties for Rent": "Explore the World’s Properties for Rent", "Furnished": "Furnished", "Pets Allowed": "<PERSON><PERSON>owed", "Smoking Allowed": "Smoking Allowed", "Online View Tour": "Online View Tour", "Rental Period": "Rental Period", "Required Documents": "Required Documents", "Highlights": "Highlights", "Rental Conditions": "Rental Conditions", "Host Speak": "Host Speak", "District Ratings": "District Ratings", "This text translated": "This text translated", "Show Original": "Show Original", "Converted from": "Converted from", "Bills Included": "Bills Included", "No clicks": "No clicks", ":number click": ":number click", ":number clicks": ":number clicks", ":number today": ":number today", "No views": "No views", ":number view": ":number view", ":number views": ":number views", "New": "New", ":count hours ago": ":count hours ago", "Yesterday": "Yesterday", "with XMetr": "with XMetr", "Rooms": "Rooms", "Posted by": "Posted by", "Last updated": "Last updated", "Long-Term": "Long-Term", "Rentals in": "Rentals in", "in": "in"}